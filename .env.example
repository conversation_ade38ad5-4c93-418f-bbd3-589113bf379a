# Playwright 自动化登录系统环境变量配置示例
# 复制此文件为 .env 并填写实际值

# 目标登录页面URL
VITE_TARGET_URL=http://************:8110/user/login?redirect=/

# 默认登录凭据（仅用于开发测试，生产环境请勿使用）
VITE_DEFAULT_USERNAME=your_username
VITE_DEFAULT_PASSWORD=your_password

# Playwright 服务器配置
PLAYWRIGHT_SERVER_PORT=3001
PLAYWRIGHT_SERVER_HOST=localhost

# Playwright 配置
PLAYWRIGHT_HEADLESS=false
PLAYWRIGHT_TIMEOUT=30000
PLAYWRIGHT_VIEWPORT_WIDTH=1280
PLAYWRIGHT_VIEWPORT_HEIGHT=720

# 开发环境配置
VITE_DEV_SERVER_PORT=5173
VITE_API_BASE_URL=http://localhost:3001/api

# 日志级别
LOG_LEVEL=info

# 安全配置
ENABLE_CORS=true
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000
