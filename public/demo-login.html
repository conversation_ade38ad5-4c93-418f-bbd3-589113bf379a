<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示登录页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .demo-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
            color: #666;
        }
        
        .demo-info h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .success-message {
            display: none;
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            text-align: center;
        }
        
        .error-message {
            display: none;
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>系统登录</h1>
            <p>请输入您的登录凭据</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" placeholder="请输入用户名" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" placeholder="请输入密码" required>
            </div>
            
            <button type="submit" class="login-btn">登录</button>
        </form>
        
        <div class="success-message" id="successMessage">
            <strong>登录成功！</strong> 正在跳转到主页...
        </div>
        
        <div class="error-message" id="errorMessage">
            <strong>登录失败！</strong> 用户名或密码错误
        </div>
        
        <div class="demo-info">
            <h3>演示说明</h3>
            <p><strong>测试账号：</strong> admin / test_user</p>
            <p><strong>测试密码：</strong> password / 123456</p>
            <p>此页面用于演示 Playwright 自动化登录功能</p>
        </div>
    </div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            
            // 隐藏之前的消息
            successMessage.style.display = 'none';
            errorMessage.style.display = 'none';
            
            // 模拟登录验证
            const validCredentials = [
                { username: 'admin', password: 'password' },
                { username: 'test_user', password: '123456' },
                { username: 'your_username', password: 'your_password' }
            ];
            
            const isValid = validCredentials.some(cred => 
                cred.username === username && cred.password === password
            );
            
            if (isValid) {
                successMessage.style.display = 'block';
                
                // 模拟跳转延迟
                setTimeout(() => {
                    // 在实际应用中，这里会跳转到主页
                    alert('登录成功！在实际应用中会跳转到主页。');
                    
                    // 可以添加一个标识元素供 Playwright 检测
                    const loginSuccessIndicator = document.createElement('div');
                    loginSuccessIndicator.id = 'login-success-indicator';
                    loginSuccessIndicator.style.display = 'none';
                    loginSuccessIndicator.textContent = 'LOGIN_SUCCESS';
                    document.body.appendChild(loginSuccessIndicator);
                    
                    // 发送消息给父窗口（如果在iframe中）
                    if (window.parent !== window) {
                        window.parent.postMessage({ type: 'LOGIN_SUCCESS', username: username }, '*');
                    }
                }, 1000);
            } else {
                errorMessage.style.display = 'block';
            }
        });
        
        // 监听来自 Playwright 的自动填充
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'AUTO_FILL') {
                document.getElementById('username').value = event.data.username || '';
                document.getElementById('password').value = event.data.password || '';
            }
        });
        
        // 添加一些调试信息
        console.log('演示登录页面已加载');
        console.log('可用的测试账号：');
        console.log('- admin / password');
        console.log('- test_user / 123456');
        console.log('- your_username / your_password');
    </script>
</body>
</html>
