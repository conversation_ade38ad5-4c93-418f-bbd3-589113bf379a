import { test, expect } from '@playwright/test';

/**
 * 演示自动登录测试脚本
 * 使用本地演示页面测试自动化登录功能
 */
test.describe('演示自动登录功能', () => {
  test('使用演示页面测试自动登录', async ({ page }) => {
    console.log('开始演示自动登录测试...');
    
    // 导航到演示登录页面
    await page.goto('/demo-login.html');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    console.log('演示登录页面已加载');
    
    // 等待登录表单出现
    await page.waitForSelector('#loginForm', { timeout: 10000 });
    console.log('登录表单已找到');
    
    // 填写用户名
    const usernameInput = page.locator('#username');
    await usernameInput.waitFor({ state: 'visible' });
    await usernameInput.fill('admin');
    console.log('用户名已填写：admin');
    
    // 填写密码
    const passwordInput = page.locator('#password');
    await passwordInput.waitFor({ state: 'visible' });
    await passwordInput.fill('password');
    console.log('密码已填写');
    
    // 点击登录按钮
    const loginButton = page.locator('.login-btn');
    await loginButton.waitFor({ state: 'visible' });
    await loginButton.click();
    console.log('登录按钮已点击');
    
    // 等待登录成功消息出现
    const successMessage = page.locator('#successMessage');
    await successMessage.waitFor({ state: 'visible', timeout: 5000 });
    console.log('登录成功消息已显示');
    
    // 验证登录成功
    await expect(successMessage).toBeVisible();
    await expect(successMessage).toContainText('登录成功');
    
    // 等待登录成功指示器（如果有的话）
    try {
      await page.waitForSelector('#login-success-indicator', { timeout: 3000 });
      console.log('登录成功指示器已找到');
    } catch (error) {
      console.log('未找到登录成功指示器，但这是正常的');
    }
    
    // 截图保存登录成功的页面
    await page.screenshot({ 
      path: 'tests/screenshots/demo-login-success.png', 
      fullPage: true 
    });
    console.log('登录成功截图已保存');
    
    console.log('演示自动登录测试完成');
  });
  
  test('测试错误的登录凭据', async ({ page }) => {
    console.log('开始测试错误登录凭据...');
    
    // 导航到演示登录页面
    await page.goto('/demo-login.html');
    await page.waitForLoadState('networkidle');
    
    // 填写错误的登录信息
    await page.fill('#username', 'wrong_user');
    await page.fill('#password', 'wrong_password');
    console.log('已填写错误的登录信息');
    
    // 点击登录按钮
    await page.click('.login-btn');
    console.log('登录按钮已点击');
    
    // 等待错误消息出现
    const errorMessage = page.locator('#errorMessage');
    await errorMessage.waitFor({ state: 'visible', timeout: 5000 });
    console.log('错误消息已显示');
    
    // 验证错误消息
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('登录失败');
    
    console.log('错误登录测试完成');
  });
  
  test('测试多种有效登录凭据', async ({ page }) => {
    const validCredentials = [
      { username: 'admin', password: 'password' },
      { username: 'test_user', password: '123456' },
      { username: 'your_username', password: 'your_password' }
    ];
    
    for (const cred of validCredentials) {
      console.log(`测试登录凭据: ${cred.username}`);
      
      // 导航到演示登录页面
      await page.goto('/demo-login.html');
      await page.waitForLoadState('networkidle');
      
      // 填写登录信息
      await page.fill('#username', cred.username);
      await page.fill('#password', cred.password);
      
      // 点击登录按钮
      await page.click('.login-btn');
      
      // 等待登录成功消息
      const successMessage = page.locator('#successMessage');
      await successMessage.waitFor({ state: 'visible', timeout: 5000 });
      
      // 验证登录成功
      await expect(successMessage).toBeVisible();
      console.log(`${cred.username} 登录成功`);
    }
    
    console.log('所有有效凭据测试完成');
  });
});

/**
 * 演示登录函数，可以被其他测试文件调用
 */
export async function demoLogin(page, username = 'admin', password = 'password') {
  console.log(`执行演示登录: ${username}`);
  
  await page.goto('/demo-login.html');
  await page.waitForLoadState('networkidle');
  
  // 填写登录信息
  await page.fill('#username', username);
  await page.fill('#password', password);
  
  // 点击登录
  await page.click('.login-btn');
  
  // 等待登录完成
  const successMessage = page.locator('#successMessage');
  await successMessage.waitFor({ state: 'visible', timeout: 5000 });
  
  console.log('演示登录完成');
  return page;
}
