import { test, expect } from '@playwright/test';

/**
 * 自动登录测试脚本
 * 用于自动化登录到指定系统
 */
test.describe('自动登录功能', () => {
  test('自动登录到系统', async ({ page }) => {
    // 导航到登录页面
    await page.goto('/user/login?redirect=/');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 等待登录表单出现
    await page.waitForSelector('form', { timeout: 10000 });
    
    // 填写用户名 - 根据实际页面结构调整选择器
    const usernameSelector = 'input[name="username"], input[type="text"], input[placeholder*="用户名"], input[placeholder*="账号"]';
    await page.waitForSelector(usernameSelector, { timeout: 5000 });
    await page.fill(usernameSelector, 'your_username'); // 请替换为实际用户名
    
    // 填写密码 - 根据实际页面结构调整选择器
    const passwordSelector = 'input[name="password"], input[type="password"], input[placeholder*="密码"]';
    await page.waitForSelector(passwordSelector, { timeout: 5000 });
    await page.fill(passwordSelector, 'your_password'); // 请替换为实际密码
    
    // 点击登录按钮 - 根据实际页面结构调整选择器
    const loginButtonSelector = 'button[type="submit"], button:has-text("登录"), button:has-text("登陆"), .login-btn, #login-btn';
    await page.waitForSelector(loginButtonSelector, { timeout: 5000 });
    await page.click(loginButtonSelector);
    
    // 等待登录完成，检查是否跳转到主页或出现登录成功的标识
    await page.waitForLoadState('networkidle');
    
    // 验证登录是否成功 - 根据实际情况调整验证逻辑
    try {
      // 方法1: 检查URL是否改变
      await page.waitForURL(/^(?!.*login).*$/, { timeout: 10000 });
      console.log('登录成功：URL已跳转');
    } catch (error) {
      // 方法2: 检查页面是否包含登录后的元素
      const loggedInElements = [
        '.user-info',
        '.logout',
        '.dashboard',
        '[data-testid="user-menu"]',
        'text=退出登录'
      ];
      
      let loginSuccess = false;
      for (const selector of loggedInElements) {
        try {
          await page.waitForSelector(selector, { timeout: 3000 });
          loginSuccess = true;
          console.log(`登录成功：找到元素 ${selector}`);
          break;
        } catch (e) {
          // 继续尝试下一个选择器
        }
      }
      
      if (!loginSuccess) {
        throw new Error('登录验证失败：未找到登录成功的标识');
      }
    }
    
    // 可选：截图保存登录后的页面
    await page.screenshot({ path: 'tests/screenshots/login-success.png', fullPage: true });
    
    console.log('自动登录流程完成');
  });
});

/**
 * 独立的登录函数，可以被其他测试文件调用
 */
export async function autoLogin(page, username = 'your_username', password = 'your_password') {
  await page.goto('/user/login?redirect=/');
  await page.waitForLoadState('networkidle');
  
  // 填写登录信息
  const usernameSelector = 'input[name="username"], input[type="text"], input[placeholder*="用户名"], input[placeholder*="账号"]';
  await page.waitForSelector(usernameSelector, { timeout: 5000 });
  await page.fill(usernameSelector, username);
  
  const passwordSelector = 'input[name="password"], input[type="password"], input[placeholder*="密码"]';
  await page.waitForSelector(passwordSelector, { timeout: 5000 });
  await page.fill(passwordSelector, password);
  
  // 点击登录
  const loginButtonSelector = 'button[type="submit"], button:has-text("登录"), button:has-text("登陆"), .login-btn, #login-btn';
  await page.waitForSelector(loginButtonSelector, { timeout: 5000 });
  await page.click(loginButtonSelector);
  
  // 等待登录完成
  await page.waitForLoadState('networkidle');
  
  return page;
}
