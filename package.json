{"name": "ai-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "playwright:install": "playwright install", "playwright:codegen": "playwright codegen", "auto-login": "playwright test tests/auto-login.spec.js --headed", "auto-login:headless": "playwright test tests/auto-login.spec.js", "demo-login": "playwright test tests/demo-login.spec.js --headed", "demo-login:headless": "playwright test tests/demo-login.spec.js", "server": "node server/playwright-server.js", "dev:full": "concurrently \"pnpm dev\" \"pnpm server\"", "setup": "pnpm install && pnpm playwright:install"}, "dependencies": {"cors": "^2.8.5", "echarts": "^5.6.0", "element-plus": "^2.10.2", "express": "^5.1.0", "pinia": "^3.0.3", "sass": "^1.89.2", "scss": "^0.2.4", "vue": "^3.5.17", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@playwright/test": "^1.54.1", "@vitejs/plugin-vue": "^6.0.0", "playwright": "^1.54.1", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}}