<template>
  <!-- elementPlus组件汉化 -->
  <el-config-provider :locale="localeZH">
    <router-view />
  </el-config-provider>
</template>

<script setup>
import localeZH from "element-plus/es/locale/lang/zh-cn";
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
}

// Element Plus 组件样式调整
.el-card {
  border-radius: 8px;

  .el-card__header {
    border-bottom: 1px solid #f0f2f5;
  }
}

.el-button {
  border-radius: 6px;
  font-weight: 500;
}

.el-input {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// 响应式工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}
</style>
