<template>
  <div class="home-page">
    <!-- 页面头部 -->
    <header class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Monitor /></el-icon>
          Playwright 自动化登录系统
        </h1>
        <p class="page-description">
          通过 Playwright 实现自动化登录，支持多种登录方式
        </p>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 功能介绍卡片 -->
      <el-row :gutter="20" class="feature-cards">
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="feature-card" shadow="hover">
            <div class="feature-icon">
              <el-icon size="40"><User /></el-icon>
            </div>
            <h3>自动登录</h3>
            <p>打开新窗口并自动填充登录信息，简化登录流程</p>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="feature-card" shadow="hover">
            <div class="feature-icon">
              <el-icon size="40"><Monitor /></el-icon>
            </div>
            <h3>Playwright 脚本</h3>
            <p>运行完整的 Playwright 自动化脚本，实现无人值守登录</p>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="feature-card" shadow="hover">
            <div class="feature-icon">
              <el-icon size="40"><Link /></el-icon>
            </div>
            <h3>直接跳转</h3>
            <p>快速跳转到目标登录页面，手动完成登录操作</p>
          </el-card>
        </el-col>
      </el-row>

      <!-- 自动登录组件 -->
      <div class="login-section">
        <AutoLoginButton />
      </div>

      <!-- 使用说明 -->
      <el-card class="instruction-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>使用说明</h3>
            <el-tag type="info">重要</el-tag>
          </div>
        </template>
        
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="配置登录信息" description="填写用户名、密码和目标URL" />
          <el-step title="选择登录方式" description="选择自动登录、Playwright脚本或直接跳转" />
          <el-step title="执行登录" description="点击按钮执行登录操作" />
          <el-step title="完成登录" description="在新窗口中完成登录流程" />
        </el-steps>
        
        <div class="instruction-content">
          <el-alert
            title="注意事项"
            type="warning"
            :closable="false"
            show-icon
          >
            <ul>
              <li>请确保目标网站允许跨域访问</li>
              <li>首次使用时请检查浏览器弹窗设置</li>
              <li>Playwright 脚本需要本地服务支持</li>
              <li>请妥善保管登录凭据，避免泄露</li>
            </ul>
          </el-alert>
          
          <div class="tech-info">
            <h4>技术栈信息</h4>
            <el-tag v-for="tech in techStack" :key="tech.name" :type="tech.type" class="tech-tag">
              {{ tech.name }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 系统状态 -->
      <el-card class="status-card" shadow="hover">
        <template #header>
          <h3>系统状态</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="status-item">
              <div class="status-label">前端服务</div>
              <el-tag type="success">运行中</el-tag>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="status-item">
              <div class="status-label">Playwright 服务</div>
              <el-tag :type="playwrightStatus ? 'success' : 'danger'">
                {{ playwrightStatus ? '已连接' : '未连接' }}
              </el-tag>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="status-item">
              <div class="status-label">目标服务</div>
              <el-tag :type="targetStatus ? 'success' : 'warning'">
                {{ targetStatus ? '可访问' : '检查中' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </main>

    <!-- 页面底部 -->
    <footer class="page-footer">
      <p>&copy; 2024 Playwright 自动化登录系统 - 基于 Vue3 + Element Plus + Playwright</p>
    </footer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Monitor, User, Link } from '@element-plus/icons-vue'
import AutoLoginButton from '@/components/AutoLoginButton.vue'
import { checkPlaywrightService } from '@/utils/playwright-runner'

// 响应式数据
const currentStep = ref(0)
const playwrightStatus = ref(false)
const targetStatus = ref(false)

// 技术栈信息
const techStack = reactive([
  { name: 'Vue 3', type: 'success' },
  { name: 'Element Plus', type: 'primary' },
  { name: 'Playwright', type: 'warning' },
  { name: 'SCSS', type: 'info' },
  { name: 'Vite', type: 'success' }
])

// 检查服务状态
const checkServices = async () => {
  try {
    // 检查 Playwright 服务
    playwrightStatus.value = await checkPlaywrightService()
    
    // 检查目标服务（简单的网络请求）
    try {
      const response = await fetch('http://************:8110/user/login?redirect=/', {
        method: 'HEAD',
        mode: 'no-cors'
      })
      targetStatus.value = true
    } catch (error) {
      targetStatus.value = false
    }
  } catch (error) {
    console.error('服务状态检查失败:', error)
  }
}

// 组件挂载时执行
onMounted(() => {
  checkServices()
  
  // 定期检查服务状态
  setInterval(checkServices, 30000) // 每30秒检查一次
})
</script>

<style scoped lang="scss">
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

.page-header {
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 40px 20px;
  text-align: center;
  
  .header-content {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .page-title {
    font-size: 2.5rem;
    color: #303133;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    
    .el-icon {
      color: #409eff;
    }
  }
  
  .page-description {
    font-size: 1.1rem;
    color: #606266;
    margin: 0;
  }
}

.main-content {
  flex: 1;
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.feature-cards {
  margin-bottom: 40px;
}

.feature-card {
  text-align: center;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
  
  .feature-icon {
    color: #409eff;
    margin-bottom: 15px;
  }
  
  h3 {
    color: #303133;
    margin: 10px 0;
    font-size: 1.2rem;
  }
  
  p {
    color: #606266;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
  }
}

.login-section {
  margin: 40px 0;
}

.instruction-card {
  margin: 40px 0;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      color: #303133;
    }
  }
  
  .instruction-content {
    margin-top: 30px;
    
    ul {
      margin: 15px 0;
      padding-left: 20px;
      
      li {
        margin: 8px 0;
        color: #606266;
      }
    }
  }
  
  .tech-info {
    margin-top: 30px;
    
    h4 {
      color: #303133;
      margin-bottom: 15px;
    }
    
    .tech-tag {
      margin: 5px 8px 5px 0;
    }
  }
}

.status-card {
  margin: 40px 0;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .status-item {
    text-align: center;
    padding: 20px 0;
    
    .status-label {
      font-size: 0.9rem;
      color: #606266;
      margin-bottom: 10px;
    }
  }
}

.page-footer {
  background: white;
  padding: 20px;
  text-align: center;
  border-top: 1px solid #ebeef5;
  
  p {
    margin: 0;
    color: #909399;
    font-size: 0.9rem;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    padding: 20px 10px;
    
    .page-title {
      font-size: 1.8rem;
      flex-direction: column;
      gap: 10px;
    }
    
    .page-description {
      font-size: 1rem;
    }
  }
  
  .main-content {
    padding: 20px 10px;
  }
  
  .feature-card {
    height: auto;
    padding: 20px;
    margin-bottom: 15px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.5rem;
  }
  
  .feature-cards {
    .el-col {
      margin-bottom: 15px;
    }
  }
}
</style>
