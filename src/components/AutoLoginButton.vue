<template>
  <div class="auto-login-container">
    <el-card class="login-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>自动登录系统</h3>
          <el-tag :type="serviceStatus ? 'success' : 'danger'">
            {{ serviceStatus ? '服务正常' : '服务离线' }}
          </el-tag>
        </div>
      </template>
      
      <el-form :model="loginForm" label-width="80px" class="login-form">
        <el-form-item label="用户名">
          <el-input 
            v-model="loginForm.username" 
            placeholder="请输入用户名"
            :disabled="isLoading"
          />
        </el-form-item>
        
        <el-form-item label="密码">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码"
            :disabled="isLoading"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="目标URL">
          <el-input 
            v-model="loginForm.targetUrl" 
            placeholder="登录页面URL"
            :disabled="isLoading"
          />
        </el-form-item>
      </el-form>
      
      <div class="button-group">
        <el-button 
          type="primary" 
          size="large"
          :loading="isLoading"
          @click="handleAutoLogin"
          :disabled="!loginForm.username || !loginForm.password"
        >
          <el-icon><User /></el-icon>
          {{ isLoading ? '正在处理...' : '自动登录' }}
        </el-button>
        
        <el-button 
          type="success" 
          size="large"
          :loading="isPlaywrightLoading"
          @click="handlePlaywrightLogin"
          :disabled="!loginForm.username || !loginForm.password"
        >
          <el-icon><Monitor /></el-icon>
          {{ isPlaywrightLoading ? '运行中...' : 'Playwright登录' }}
        </el-button>
        
        <el-button 
          size="large"
          @click="handleDirectJump"
        >
          <el-icon><Link /></el-icon>
          直接跳转
        </el-button>
      </div>
      
      <div class="status-info" v-if="statusMessage">
        <el-alert 
          :title="statusMessage" 
          :type="alertType"
          :closable="false"
          show-icon
        />
      </div>
    </el-card>
    
    <!-- 操作日志 -->
    <el-card class="log-card" shadow="hover" v-if="logs.length > 0">
      <template #header>
        <div class="card-header">
          <h4>操作日志</h4>
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </template>
      
      <div class="log-content">
        <div 
          v-for="(log, index) in logs" 
          :key="index" 
          class="log-item"
          :class="log.type"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Monitor, Link } from '@element-plus/icons-vue'
import { triggerAutoLogin, triggerPlaywrightScript, checkPlaywrightService, createAutoFillScript } from '@/utils/playwright-runner'

// 响应式数据
const isLoading = ref(false)
const isPlaywrightLoading = ref(false)
const serviceStatus = ref(false)
const statusMessage = ref('')
const alertType = ref('info')
const logs = ref([])

// 表单数据
const loginForm = reactive({
  username: import.meta.env.VITE_DEFAULT_USERNAME || 'your_username', // 从环境变量读取默认用户名
  password: import.meta.env.VITE_DEFAULT_PASSWORD || 'your_password', // 从环境变量读取默认密码
  targetUrl: import.meta.env.VITE_TARGET_URL || 'http://************:8110/user/login?redirect=/'
})

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 检查服务状态
const checkService = async () => {
  try {
    serviceStatus.value = await checkPlaywrightService()
    addLog(`服务状态检查: ${serviceStatus.value ? '正常' : '离线'}`, serviceStatus.value ? 'success' : 'error')
  } catch (error) {
    serviceStatus.value = false
    addLog('服务状态检查失败: ' + error.message, 'error')
  }
}

// 处理自动登录（打开新窗口方式）
const handleAutoLogin = async () => {
  isLoading.value = true
  statusMessage.value = '正在打开登录页面...'
  alertType.value = 'info'
  
  try {
    addLog('开始自动登录流程...', 'info')
    
    const success = await triggerAutoLogin({
      username: loginForm.username,
      password: loginForm.password,
      targetUrl: loginForm.targetUrl
    })
    
    if (success) {
      statusMessage.value = '登录页面已打开，请在新窗口中完成登录'
      alertType.value = 'success'
      addLog('登录页面已成功打开', 'success')
      
      // 可选：创建自动填充脚本
      const autoFillScript = createAutoFillScript(loginForm.username, loginForm.password)
      console.log('自动填充脚本已生成，可在浏览器控制台中执行:', autoFillScript)
      addLog('自动填充脚本已生成', 'info')
      
      ElMessage.success('登录页面已打开，请查看新窗口')
    } else {
      throw new Error('无法打开登录页面')
    }
  } catch (error) {
    statusMessage.value = '自动登录失败: ' + error.message
    alertType.value = 'error'
    addLog('自动登录失败: ' + error.message, 'error')
    ElMessage.error('自动登录失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 处理Playwright脚本登录
const handlePlaywrightLogin = async () => {
  isPlaywrightLoading.value = true
  statusMessage.value = '正在运行Playwright脚本...'
  alertType.value = 'info'
  
  try {
    addLog('开始运行Playwright脚本...', 'info')
    
    const result = await triggerPlaywrightScript('auto-login', {
      username: loginForm.username,
      password: loginForm.password,
      targetUrl: loginForm.targetUrl
    })
    
    statusMessage.value = 'Playwright脚本执行完成'
    alertType.value = 'success'
    addLog('Playwright脚本执行成功', 'success')
    ElMessage.success('Playwright脚本执行完成')
    
    console.log('Playwright执行结果:', result)
  } catch (error) {
    statusMessage.value = 'Playwright脚本执行失败: ' + error.message
    alertType.value = 'error'
    addLog('Playwright脚本执行失败: ' + error.message, 'error')
    ElMessage.error('Playwright脚本执行失败: ' + error.message)
  } finally {
    isPlaywrightLoading.value = false
  }
}

// 直接跳转到登录页面
const handleDirectJump = () => {
  addLog('直接跳转到登录页面...', 'info')
  window.open(loginForm.targetUrl, '_blank')
  ElMessage.info('已打开登录页面')
}

// 组件挂载时检查服务状态
onMounted(() => {
  checkService()
  addLog('组件已加载', 'info')
})
</script>

<style scoped lang="scss">
.auto-login-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.login-card, .log-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3, h4 {
    margin: 0;
    color: #303133;
  }
}

.login-form {
  margin: 20px 0;
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin: 20px 0;
}

.status-info {
  margin-top: 20px;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 12px;
  line-height: 1.4;
  
  &.success {
    color: #67c23a;
  }
  
  &.error {
    color: #f56c6c;
  }
  
  &.info {
    color: #909399;
  }
}

.log-time {
  min-width: 80px;
  margin-right: 10px;
  color: #909399;
}

.log-message {
  flex: 1;
}

// 响应式设计
@media (max-width: 768px) {
  .auto-login-container {
    padding: 10px;
  }
  
  .button-group {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
