/**
 * Playwright 运行器工具
 * 用于从前端触发 Playwright 自动化脚本
 */

/**
 * 触发自动登录
 * @param {Object} options 登录选项
 * @param {string} options.username 用户名
 * @param {string} options.password 密码
 * @param {string} options.targetUrl 目标URL
 * @returns {Promise<boolean>} 是否成功触发
 */
export async function triggerAutoLogin(options = {}) {
  const {
    username = 'your_username',
    password = 'your_password',
    targetUrl = 'http://10.186.10.24:8110/user/login?redirect=/'
  } = options;

  try {
    // 方法1: 直接打开新窗口到目标URL，让用户手动操作
    // 这是最简单的方法，不需要后端支持
    const newWindow = window.open(targetUrl, '_blank', 'width=1200,height=800');
    
    if (newWindow) {
      // 可选：监听新窗口的消息，了解登录状态
      const messageHandler = (event) => {
        if (event.origin === new URL(targetUrl).origin) {
          console.log('收到来自登录页面的消息:', event.data);
          // 处理登录成功/失败的消息
        }
      };
      
      window.addEventListener('message', messageHandler);
      
      // 清理事件监听器
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
      }, 30000); // 30秒后清理
      
      return true;
    } else {
      throw new Error('无法打开新窗口，可能被浏览器阻止');
    }
  } catch (error) {
    console.error('触发自动登录失败:', error);
    return false;
  }
}

/**
 * 通过本地服务触发 Playwright 脚本
 * 需要配合本地 Express 服务使用
 */
export async function triggerPlaywrightScript(scriptName = 'auto-login', params = {}) {
  try {
    const response = await fetch('/api/run-playwright', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        script: scriptName,
        params: params
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    return result;
  } catch (error) {
    console.error('运行 Playwright 脚本失败:', error);
    throw error;
  }
}

/**
 * 检查 Playwright 服务状态
 */
export async function checkPlaywrightService() {
  try {
    const response = await fetch('/api/playwright-status');
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * 创建自动填充脚本注入到目标页面
 * 这个方法可以在新打开的窗口中注入自动填充脚本
 */
export function createAutoFillScript(username, password) {
  return `
    (function() {
      console.log('开始自动填充登录信息...');
      
      // 等待页面加载完成
      function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
          const startTime = Date.now();
          
          function check() {
            const element = document.querySelector(selector);
            if (element) {
              resolve(element);
            } else if (Date.now() - startTime > timeout) {
              reject(new Error('元素未找到: ' + selector));
            } else {
              setTimeout(check, 100);
            }
          }
          
          check();
        });
      }
      
      // 自动填充函数
      async function autoFill() {
        try {
          // 查找用户名输入框
          const usernameSelectors = [
            'input[name="username"]',
            'input[type="text"]',
            'input[placeholder*="用户名"]',
            'input[placeholder*="账号"]'
          ];
          
          let usernameInput = null;
          for (const selector of usernameSelectors) {
            try {
              usernameInput = await waitForElement(selector, 2000);
              break;
            } catch (e) {
              continue;
            }
          }
          
          if (usernameInput) {
            usernameInput.value = '${username}';
            usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
            usernameInput.dispatchEvent(new Event('change', { bubbles: true }));
            console.log('用户名已填充');
          }
          
          // 查找密码输入框
          const passwordSelectors = [
            'input[name="password"]',
            'input[type="password"]',
            'input[placeholder*="密码"]'
          ];
          
          let passwordInput = null;
          for (const selector of passwordSelectors) {
            try {
              passwordInput = await waitForElement(selector, 2000);
              break;
            } catch (e) {
              continue;
            }
          }
          
          if (passwordInput) {
            passwordInput.value = '${password}';
            passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
            passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
            console.log('密码已填充');
          }
          
          // 可选：自动点击登录按钮
          const loginButtonSelectors = [
            'button[type="submit"]',
            'button:contains("登录")',
            'button:contains("登陆")',
            '.login-btn',
            '#login-btn'
          ];
          
          for (const selector of loginButtonSelectors) {
            try {
              const button = await waitForElement(selector, 2000);
              if (button) {
                console.log('找到登录按钮，准备点击...');
                // 延迟点击，给用户确认的时间
                setTimeout(() => {
                  button.click();
                  console.log('登录按钮已点击');
                }, 1000);
                break;
              }
            } catch (e) {
              continue;
            }
          }
          
        } catch (error) {
          console.error('自动填充失败:', error);
        }
      }
      
      // 页面加载完成后执行
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoFill);
      } else {
        autoFill();
      }
    })();
  `;
}
