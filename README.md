# Playwright 自动化登录系统

基于 Vue3 + Element Plus + Playwright 的自动化登录系统，支持多种登录方式。

## 功能特性

- 🚀 **自动登录**: 打开新窗口并自动填充登录信息
- 🤖 **Playwright 脚本**: 运行完整的自动化登录脚本
- 🔗 **直接跳转**: 快速跳转到目标登录页面
- 📱 **响应式设计**: 支持桌面端和移动端
- 🎨 **现代化UI**: 基于 Element Plus 的美观界面
- 📊 **实时状态**: 显示服务状态和操作日志

## 技术栈

- **前端**: Vue 3 + Element Plus + SCSS + Vite
- **自动化**: Playwright
- **后端**: Express.js (可选)
- **包管理**: pnpm

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 安装 Playwright 浏览器

```bash
pnpm playwright:install
```

### 3. 配置登录信息

编辑以下文件中的登录信息：

- `src/components/AutoLoginButton.vue` - 修改默认用户名和密码
- `tests/auto-login.spec.js` - 修改测试脚本中的登录信息
- `src/utils/playwright-runner.js` - 修改工具函数中的默认值

### 4. 启动开发服务器

```bash
# 仅启动前端
pnpm dev

# 同时启动前端和后端服务
pnpm dev:full
```

### 5. 访问应用

打开浏览器访问 `http://localhost:5173`

## 使用方法

### 方式一：自动登录（推荐）

1. 在页面中填写用户名、密码和目标URL
2. 点击"自动登录"按钮
3. 系统会打开新窗口并自动填充登录信息
4. 在新窗口中完成登录流程

### 方式二：Playwright 脚本

1. 确保后端服务已启动 (`pnpm server`)
2. 填写登录信息
3. 点击"Playwright登录"按钮
4. 系统会运行完整的自动化脚本

### 方式三：直接跳转

1. 点击"直接跳转"按钮
2. 在新窗口中手动完成登录

## 脚本命令

```bash
# 开发相关
pnpm dev                    # 启动前端开发服务器
pnpm server                 # 启动后端服务器
pnpm dev:full              # 同时启动前后端服务

# Playwright 相关
pnpm test                   # 运行所有测试
pnpm test:headed           # 有头模式运行测试
pnpm test:ui               # UI 模式运行测试
pnpm auto-login            # 运行自动登录脚本（有头模式）
pnpm auto-login:headless   # 运行自动登录脚本（无头模式）
pnpm playwright:codegen    # 启动代码生成器

# 构建相关
pnpm build                 # 构建生产版本
pnpm preview               # 预览构建结果
```

## 配置说明

### Playwright 配置

编辑 `playwright.config.js` 文件来修改 Playwright 配置：

- `baseURL`: 目标网站的基础URL
- `viewport`: 浏览器窗口大小
- `timeout`: 操作超时时间
- `browsers`: 支持的浏览器类型

### 登录脚本配置

编辑 `tests/auto-login.spec.js` 文件来修改登录逻辑：

- 修改选择器以匹配目标网站的表单元素
- 调整等待时间和超时设置
- 添加自定义的验证逻辑

## 目录结构

```
├── src/
│   ├── components/          # Vue 组件
│   │   └── AutoLoginButton.vue
│   ├── views/              # 页面组件
│   │   └── HomePage.vue
│   ├── utils/              # 工具函数
│   │   └── playwright-runner.js
│   ├── router/             # 路由配置
│   └── App.vue
├── tests/                  # Playwright 测试
│   └── auto-login.spec.js
├── server/                 # 后端服务（可选）
│   └── playwright-server.js
├── playwright.config.js    # Playwright 配置
└── package.json
```

## 注意事项

1. **安全性**: 请妥善保管登录凭据，避免在代码中硬编码敏感信息
2. **跨域**: 确保目标网站允许跨域访问或配置适当的代理
3. **浏览器设置**: 检查浏览器的弹窗阻止设置
4. **网络环境**: 确保能够访问目标登录页面
5. **选择器**: 根据目标网站的实际结构调整CSS选择器

## 故障排除

### 常见问题

1. **无法打开新窗口**
   - 检查浏览器弹窗设置
   - 确保在用户交互后触发

2. **Playwright 脚本执行失败**
   - 检查选择器是否正确
   - 确认网络连接正常
   - 查看控制台错误信息

3. **服务连接失败**
   - 确保后端服务已启动
   - 检查端口是否被占用
   - 验证API接口地址

### 调试技巧

1. 使用 `pnpm test:debug` 进入调试模式
2. 使用 `pnpm playwright:codegen` 生成新的脚本
3. 查看浏览器开发者工具的网络和控制台面板
4. 检查 Playwright 测试报告

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
