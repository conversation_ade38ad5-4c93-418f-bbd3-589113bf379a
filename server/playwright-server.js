import express from 'express';
import cors from 'cors';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'playwright-server'
  });
});

// Playwright 服务状态检查
app.get('/api/playwright-status', (req, res) => {
  res.json({ 
    status: 'available', 
    timestamp: new Date().toISOString(),
    playwright: true
  });
});

// 运行 Playwright 脚本
app.post('/api/run-playwright', async (req, res) => {
  const { script = 'auto-login', params = {} } = req.body;
  
  try {
    console.log(`运行 Playwright 脚本: ${script}`, params);
    
    // 构建命令
    const projectRoot = path.resolve(__dirname, '..');
    const scriptPath = `tests/${script}.spec.js`;
    
    // 设置环境变量传递参数
    const env = {
      ...process.env,
      PLAYWRIGHT_USERNAME: params.username || 'your_username',
      PLAYWRIGHT_PASSWORD: params.password || 'your_password',
      PLAYWRIGHT_TARGET_URL: params.targetUrl || 'http://************:8110/user/login?redirect=/'
    };
    
    // 执行 Playwright 命令
    const playwrightProcess = spawn('npx', ['playwright', 'test', scriptPath, '--headed'], {
      cwd: projectRoot,
      env: env,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    playwrightProcess.stdout.on('data', (data) => {
      stdout += data.toString();
      console.log('Playwright stdout:', data.toString());
    });
    
    playwrightProcess.stderr.on('data', (data) => {
      stderr += data.toString();
      console.error('Playwright stderr:', data.toString());
    });
    
    playwrightProcess.on('close', (code) => {
      console.log(`Playwright 进程退出，代码: ${code}`);
      
      if (code === 0) {
        res.json({
          success: true,
          message: 'Playwright 脚本执行成功',
          stdout: stdout,
          stderr: stderr,
          exitCode: code
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Playwright 脚本执行失败',
          stdout: stdout,
          stderr: stderr,
          exitCode: code
        });
      }
    });
    
    playwrightProcess.on('error', (error) => {
      console.error('Playwright 进程错误:', error);
      res.status(500).json({
        success: false,
        message: 'Playwright 进程启动失败',
        error: error.message
      });
    });
    
    // 设置超时
    setTimeout(() => {
      if (!playwrightProcess.killed) {
        playwrightProcess.kill();
        res.status(408).json({
          success: false,
          message: 'Playwright 脚本执行超时',
          timeout: true
        });
      }
    }, 60000); // 60秒超时
    
  } catch (error) {
    console.error('运行 Playwright 脚本时出错:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 获取可用的 Playwright 脚本列表
app.get('/api/playwright-scripts', (req, res) => {
  const scripts = [
    {
      name: 'auto-login',
      description: '自动登录脚本',
      file: 'tests/auto-login.spec.js'
    }
  ];
  
  res.json({
    scripts: scripts,
    count: scripts.length
  });
});

// 生成 Playwright 代码
app.post('/api/playwright-codegen', (req, res) => {
  const { url = 'http://************:8110/user/login?redirect=/' } = req.body;
  
  try {
    const projectRoot = path.resolve(__dirname, '..');
    
    // 启动 Playwright 代码生成器
    const codegenProcess = spawn('npx', ['playwright', 'codegen', url], {
      cwd: projectRoot,
      detached: true,
      stdio: 'ignore'
    });
    
    codegenProcess.unref();
    
    res.json({
      success: true,
      message: 'Playwright 代码生成器已启动',
      url: url
    });
    
  } catch (error) {
    console.error('启动代码生成器时出错:', error);
    res.status(500).json({
      success: false,
      message: '启动代码生成器失败',
      error: error.message
    });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: error.message
  });
});

// 404 处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.path
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`Playwright 服务器运行在 http://localhost:${PORT}`);
  console.log('可用的接口:');
  console.log('  GET  /api/health - 健康检查');
  console.log('  GET  /api/playwright-status - Playwright 状态');
  console.log('  POST /api/run-playwright - 运行 Playwright 脚本');
  console.log('  GET  /api/playwright-scripts - 获取脚本列表');
  console.log('  POST /api/playwright-codegen - 启动代码生成器');
});

export default app;
